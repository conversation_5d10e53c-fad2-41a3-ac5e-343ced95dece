<script lang="ts">
	import ForwardIcon from "@/assets/svg/forward-icon.svelte";

	import MinusIcon from "@/assets/svg/minus-icon.svelte";
	import MutedIcon from "@/assets/svg/muted-icon.svelte";

	import PauseIcon from "@/assets/svg/pause-icon.svelte";
	import PlayIcon from "@/assets/svg/play-icon.svelte";
	import PlusIcon from "@/assets/svg/plus-icon.svelte";
	import PrevIcon from "@/assets/svg/prev-icon.svelte";
	import RewindIcon from "@/assets/svg/rewind-icon.svelte";
	import ShuffleIcon from "@/assets/svg/shuffle-icon.svelte";
	import VolumeIcon from "@/assets/svg/volume-icon.svelte";
	import type { ITab } from "@/interfaces/tab";
	import { YtEvents, dispatchYtEvent, seekVideoYt, setYtVolume, shuffleYt, type SeekVideoYtArgs, type SetYtVolumeArgs } from "@/utils/yt-utils";
	import Button from "./button.svelte";
	import ArrowRight from "./icons/arrow-right.svelte";
	import VideoPlaylist from "./video-playlist.svelte";
	import { secondToTimeString } from "@/utils/second-to-time-string";

	export let tab: ITab;
	export let tabs: ITab[];
	export let idx: number;

	let showPlaylist = idx === 0 && tab.isYoutube && tab.isPlaylist;
	let youtubeVolume = tab.volume;
	let seekPosition = tab.duration === 0 ? 0 : (tab.currentTime / tab.duration) * 100;

	const toggleMute = async (tabId: number, state: boolean) => {
		await chrome.tabs.update(tabId, { muted: !state });
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isMuted = !state;
			}
			return tab;
		});
	};

	const refreshTabs = () => {
		setTimeout(async () => {
			const newTab = await chrome.tabs.get(tab.id);
			tabs = tabs.map((el) => {
				if (el.id === tab.id) el.title = newTab.title ?? "";
				return el;
			});
		}, 2000);
	};

	const setVolume = async () => {
		await chrome.scripting.executeScript<SetYtVolumeArgs, number>({
			func: setYtVolume,
			target: { tabId: tab.id },
			world: "MAIN",
			args: [youtubeVolume],
		});
	};



	const prev = async (tabId: number) => {
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [YtEvents.prev()],
		});

		refreshTabs();
	};

	const togglePlay = async (tabId: number) => {
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isPaused = !tab.isPaused;
			}
			return tab;
		});
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [tab.isYoutubeMusic ? YtEvents.playToggleMusic() : YtEvents.playToggle()],
		});
	};



	const toggleShuffle = async (tabId: number) => {
		if (tab.isYoutubeMusic) {
			await chrome.scripting.executeScript({
				func: dispatchYtEvent,
				target: { tabId },
				args: [YtEvents.shuffleMusic()],
			});
			return;
		}

		await chrome.scripting.executeScript({
			func: shuffleYt,
			target: { tabId },
			world: "MAIN",
		});
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isShuffled = !tab.isShuffled;
			}
			return tab;
		});
	};

	const seekVideo = async (tabId: number, second: number, to?: number) => {
		await chrome.scripting.executeScript<SeekVideoYtArgs, void>({
			func: seekVideoYt,
			target: { tabId },
			world: "MAIN",
			args: [{ second, to }],
		});
	};

	const openTab = async () => {
		await chrome.tabs.update(tab.id, {
			active: true,
		});
	};
</script>

<div class="flex w-full justify-between border border-gray-200 dark:border-gray-600 p-4 gap-3 rounded-xl flex-col
			 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700
			 shadow-lg hover:shadow-xl transition-all duration-200 relative">
	<button on:click={openTab} title="Open Tab"
			class="neu-open-tab-btn absolute top-3 right-3">
		<ArrowRight width={18} />
	</button>
	<div class="flex items-start gap-2 min-w-0 pr-10">
		{#if tab.iconUrl}
			<img width="18px" src={tab.iconUrl} alt="icon" />
		{/if}
		<span class="text-sm font-medium text-gray-800 dark:text-gray-200 break-words leading-relaxed" title={tab.title}>
			{tab.title}
		</span>
	</div>
	{#if tab.duration}
		<div class="flex items-center gap-3">
			<span class="text-sm text-gray-700 dark:text-gray-300 font-medium min-w-[50px]">
				{secondToTimeString(Math.floor((seekPosition / 100) * tab.duration))}
			</span>
			<input
				bind:value={seekPosition}
				type="range"
				min="0"
				max={100}
				class="neu-progress-bar flex-1"
				on:change={() => seekVideo(tab.id, 0, (seekPosition / 100) * tab.duration)}
			/>
			<span class="text-sm text-gray-700 dark:text-gray-300 font-medium min-w-[50px]">
				{secondToTimeString(Math.floor(tab.duration))}
			</span>
		</div>
	{/if}
	<div class="flex gap-4 items-center">
		<button
			title="Mute/Unmute"
			on:click={() => toggleMute(tab.id, tab.isMuted)}
			class="neu-media-control-btn"
		>
			{#if tab.isMuted}
				<MutedIcon width={16} />
			{:else}
				<VolumeIcon width={16} />
			{/if}
		</button>
		{#if tab.isYoutube}
			<div class="flex gap-3 items-center">
				<input
					bind:value={youtubeVolume}
					type="range"
					min="0"
					max="100"
					class="neu-volume-bar w-20"
					on:change={setVolume}
				/>
				<span class="neu-volume-display">
					{Math.floor(youtubeVolume)}
				</span>
			</div>
			<div class="flex items-center justify-center gap-3">
				<!-- 快退按钮 -->
				<button
					title="Rewind 10s"
					class="neu-media-control-btn"
					on:click={() => seekVideo(tab.id, -10)}
				>
					<RewindIcon width={16} />
				</button>

				<!-- 上一首按钮 (仅播放列表显示) -->
				<button
					title="Prev"
					class={`neu-media-control-btn ${!tab.isPlaylist ? "invisible" : "visible"}`}
					on:click={() => prev(tab.id)}
				>
					<PrevIcon width={16} />
				</button>

				<!-- 播放/暂停按钮 (加大尺寸) -->
				<button
					title="Play/Pause"
					class="neu-media-control-btn-large"
					on:click={() => togglePlay(tab.id)}
				>
					{#if tab.isPaused}
						<PlayIcon width={20} />
					{:else}
						<PauseIcon width={20} />
					{/if}
				</button>

				<!-- 快进按钮 -->
				<button
					title="Forward 10s"
					class="neu-media-control-btn"
					on:click={() => seekVideo(tab.id, 10)}
				>
					<ForwardIcon width={16} />
				</button>
			</div>
			<div class="flex gap-2">
				{#if tab.isPlaylist && !tab.isPlaylistMix}
					<button
						title={`Shuffle ${tab.isShuffled ? "ON" : "OFF"}`}
						class={`neu-media-control-btn ${tab.isShuffled ? "neu-shuffle-active" : ""}`}
						on:click={() => toggleShuffle(tab.id)}
					>
						<ShuffleIcon width={16} />
					</button>
				{/if}
			</div>
			{#if tab.isPlaylist}
				<div class="ml-auto">
					<button
						class={`neu-media-control-btn text-base transition-transform duration-300
								${showPlaylist ? "" : "-rotate-90"}`}
						on:click={() => (showPlaylist = !showPlaylist)}
					>
						🔽
					</button>
				</div>
			{/if}
		{/if}
	</div>
	{#if showPlaylist}
		<VideoPlaylist {refreshTabs} tabId={tab.id} />
	{/if}
</div>

<style>
	/* 莫兰迪色系半透明媒体控制按钮 */
	:global(.neu-media-control-btn) {
		width: 2.75rem;
		height: 2.75rem;
		border-radius: 0.75rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #8B7D7B;
		fill: #8B7D7B;
		background: rgba(245, 240, 235, 0.85);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.3);
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow:
			4px 4px 12px rgba(139, 125, 123, 0.15),
			-4px -4px 12px rgba(255, 255, 255, 0.7),
			inset 1px 1px 2px rgba(255, 255, 255, 0.5);
	}

	:global(.neu-media-control-btn:hover) {
		transform: translateY(-2px) scale(1.02);
		color: #6B5B58;
		fill: #6B5B58;
		background: rgba(245, 240, 235, 0.95);
		box-shadow:
			6px 6px 18px rgba(139, 125, 123, 0.2),
			-6px -6px 18px rgba(255, 255, 255, 0.8),
			inset 1px 1px 3px rgba(255, 255, 255, 0.6);
	}

	:global(.neu-media-control-btn:active) {
		transform: translateY(0) scale(0.98);
		background: rgba(235, 230, 225, 0.9);
		box-shadow:
			inset 3px 3px 8px rgba(139, 125, 123, 0.2),
			inset -3px -3px 8px rgba(255, 255, 255, 0.6);
	}

	/* 大尺寸播放/暂停按钮 - 莫兰迪玫瑰金色调 */
	:global(.neu-media-control-btn-large) {
		width: 3.5rem;
		height: 3.5rem;
		border-radius: 1rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #A67C7C;
		fill: #A67C7C;
		background: rgba(240, 220, 215, 0.9);
		backdrop-filter: blur(12px);
		border: 1px solid rgba(255, 255, 255, 0.4);
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow:
			6px 6px 16px rgba(166, 124, 124, 0.2),
			-6px -6px 16px rgba(255, 255, 255, 0.8),
			inset 1px 1px 3px rgba(255, 255, 255, 0.6);
	}

	:global(.neu-media-control-btn-large:hover) {
		transform: translateY(-3px) scale(1.05);
		color: #8B5A5A;
		fill: #8B5A5A;
		background: rgba(240, 220, 215, 1);
		box-shadow:
			8px 8px 24px rgba(166, 124, 124, 0.25),
			-8px -8px 24px rgba(255, 255, 255, 0.9),
			inset 1px 1px 4px rgba(255, 255, 255, 0.7);
	}

	:global(.neu-media-control-btn-large:active) {
		transform: translateY(0) scale(1);
		background: rgba(230, 210, 205, 0.95);
		box-shadow:
			inset 4px 4px 12px rgba(166, 124, 124, 0.25),
			inset -4px -4px 12px rgba(255, 255, 255, 0.7);
	}

	/* 莫兰迪色系半透明进度条 */
	:global(.neu-progress-bar) {
		-webkit-appearance: none;
		appearance: none;
		height: 0.75rem;
		border-radius: 0.375rem;
		background: rgba(220, 210, 200, 0.6);
		backdrop-filter: blur(8px);
		border: 1px solid rgba(255, 255, 255, 0.2);
		box-shadow:
			inset 2px 2px 6px rgba(139, 125, 123, 0.15),
			inset -2px -2px 6px rgba(255, 255, 255, 0.6);
		outline: none;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	}

	:global(.neu-progress-bar::-webkit-slider-thumb) {
		-webkit-appearance: none;
		appearance: none;
		width: 1.25rem;
		height: 1.25rem;
		border-radius: 50%;
		background: rgba(180, 150, 140, 0.9);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.3);
		box-shadow:
			3px 3px 8px rgba(139, 125, 123, 0.2),
			-3px -3px 8px rgba(255, 255, 255, 0.7),
			inset 1px 1px 2px rgba(255, 255, 255, 0.5);
		cursor: pointer;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	}

	:global(.neu-progress-bar::-webkit-slider-thumb:hover) {
		transform: scale(1.15);
		background: rgba(160, 130, 120, 0.95);
		box-shadow:
			4px 4px 12px rgba(139, 125, 123, 0.25),
			-4px -4px 12px rgba(255, 255, 255, 0.8),
			inset 1px 1px 3px rgba(255, 255, 255, 0.6);
	}

	/* 莫兰迪色系半透明音量条 */
	:global(.neu-volume-bar) {
		-webkit-appearance: none;
		appearance: none;
		height: 0.5rem;
		border-radius: 0.25rem;
		background: rgba(210, 200, 190, 0.6);
		backdrop-filter: blur(6px);
		border: 1px solid rgba(255, 255, 255, 0.2);
		box-shadow:
			inset 1px 1px 4px rgba(139, 125, 123, 0.12),
			inset -1px -1px 4px rgba(255, 255, 255, 0.5);
		outline: none;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	}

	:global(.neu-volume-bar::-webkit-slider-thumb) {
		-webkit-appearance: none;
		appearance: none;
		width: 1rem;
		height: 1rem;
		border-radius: 50%;
		background: rgba(170, 140, 130, 0.9);
		backdrop-filter: blur(8px);
		border: 1px solid rgba(255, 255, 255, 0.3);
		box-shadow:
			2px 2px 6px rgba(139, 125, 123, 0.18),
			-2px -2px 6px rgba(255, 255, 255, 0.6),
			inset 1px 1px 2px rgba(255, 255, 255, 0.4);
		cursor: pointer;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	}

	:global(.neu-volume-bar::-webkit-slider-thumb:hover) {
		transform: scale(1.2);
		background: rgba(150, 120, 110, 0.95);
		box-shadow:
			3px 3px 8px rgba(139, 125, 123, 0.22),
			-3px -3px 8px rgba(255, 255, 255, 0.7),
			inset 1px 1px 3px rgba(255, 255, 255, 0.5);
	}

	/* 莫兰迪色系音量显示 */
	:global(.neu-volume-display) {
		background: rgba(200, 185, 175, 0.8);
		backdrop-filter: blur(8px);
		border: 1px solid rgba(255, 255, 255, 0.25);
		color: #8B7D7B;
		width: 2.5rem;
		height: 2rem;
		border-radius: 0.5rem;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 600;
		font-size: 0.75rem;
		box-shadow:
			inset 1px 1px 3px rgba(139, 125, 123, 0.15),
			inset -1px -1px 3px rgba(255, 255, 255, 0.5);
		transition: all 0.3s ease;
	}

	/* 深色模式 - 莫兰迪深色调 */
	:global(.dark) :global(.neu-media-control-btn) {
		color: #B8A8A6;
		fill: #B8A8A6;
		background: rgba(60, 55, 50, 0.85);
		border: 1px solid rgba(255, 255, 255, 0.1);
		box-shadow:
			4px 4px 12px rgba(0, 0, 0, 0.4),
			-4px -4px 12px rgba(255, 255, 255, 0.03),
			inset 1px 1px 2px rgba(255, 255, 255, 0.08);
	}

	:global(.dark) :global(.neu-media-control-btn:hover) {
		color: #D4C4C2;
		fill: #D4C4C2;
		background: rgba(70, 65, 60, 0.9);
		box-shadow:
			6px 6px 18px rgba(0, 0, 0, 0.5),
			-6px -6px 18px rgba(255, 255, 255, 0.05),
			inset 1px 1px 3px rgba(255, 255, 255, 0.1);
	}

	:global(.dark) :global(.neu-media-control-btn-large) {
		color: #C4A8A6;
		fill: #C4A8A6;
		background: rgba(70, 60, 55, 0.9);
		border: 1px solid rgba(255, 255, 255, 0.12);
		box-shadow:
			6px 6px 16px rgba(0, 0, 0, 0.45),
			-6px -6px 16px rgba(255, 255, 255, 0.04),
			inset 1px 1px 3px rgba(255, 255, 255, 0.1);
	}

	:global(.dark) :global(.neu-media-control-btn-large:hover) {
		color: #E0C4C2;
		fill: #E0C4C2;
		background: rgba(80, 70, 65, 0.95);
		box-shadow:
			8px 8px 24px rgba(0, 0, 0, 0.55),
			-8px -8px 24px rgba(255, 255, 255, 0.06),
			inset 1px 1px 4px rgba(255, 255, 255, 0.12);
	}

	:global(.dark) :global(.neu-progress-bar),
	:global(.dark) :global(.neu-volume-bar) {
		background: rgba(50, 45, 40, 0.7);
		border: 1px solid rgba(255, 255, 255, 0.08);
		box-shadow:
			inset 2px 2px 6px rgba(0, 0, 0, 0.4),
			inset -2px -2px 6px rgba(255, 255, 255, 0.03);
	}

	:global(.dark) :global(.neu-progress-bar::-webkit-slider-thumb),
	:global(.dark) :global(.neu-volume-bar::-webkit-slider-thumb) {
		background: rgba(120, 100, 90, 0.9);
		border: 1px solid rgba(255, 255, 255, 0.1);
		box-shadow:
			3px 3px 8px rgba(0, 0, 0, 0.5),
			-3px -3px 8px rgba(255, 255, 255, 0.04),
			inset 1px 1px 2px rgba(255, 255, 255, 0.08);
	}

	:global(.dark) :global(.neu-volume-display) {
		background: rgba(60, 55, 50, 0.8);
		border: 1px solid rgba(255, 255, 255, 0.1);
		color: #B8A8A6;
		box-shadow:
			inset 1px 1px 3px rgba(0, 0, 0, 0.3),
			inset -1px -1px 3px rgba(255, 255, 255, 0.05);
	}

	/* Shuffle按钮激活状态 - 莫兰迪薄荷绿 */
	:global(.neu-shuffle-active) {
		background: rgba(150, 180, 160, 0.9) !important;
		backdrop-filter: blur(12px) !important;
		border: 1px solid rgba(255, 255, 255, 0.4) !important;
		color: #5A6B5D !important;
		fill: #5A6B5D !important;
		box-shadow:
			4px 4px 12px rgba(150, 180, 160, 0.3),
			-4px -4px 12px rgba(255, 255, 255, 0.8),
			inset 1px 1px 3px rgba(255, 255, 255, 0.6) !important;
	}

	:global(.dark) :global(.neu-shuffle-active) {
		background: rgba(80, 100, 85, 0.9) !important;
		border: 1px solid rgba(255, 255, 255, 0.15) !important;
		color: #A8C4B0 !important;
		fill: #A8C4B0 !important;
		box-shadow:
			4px 4px 12px rgba(80, 100, 85, 0.4),
			-4px -4px 12px rgba(255, 255, 255, 0.06),
			inset 1px 1px 3px rgba(255, 255, 255, 0.1) !important;
	}

	/* Open Tab按钮 - 莫兰迪色系 */
	:global(.neu-open-tab-btn) {
		width: 2rem;
		height: 2rem;
		border-radius: 0.5rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		fill: #A67C7C;
		background: rgba(230, 220, 210, 0.8);
		backdrop-filter: blur(8px);
		border: 1px solid rgba(255, 255, 255, 0.3);
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow:
			3px 3px 8px rgba(166, 124, 124, 0.15),
			-3px -3px 8px rgba(255, 255, 255, 0.7),
			inset 1px 1px 2px rgba(255, 255, 255, 0.5);
	}

	:global(.neu-open-tab-btn:hover) {
		transform: translateY(-1px) scale(1.05);
		fill: #8B5A5A;
		background: rgba(230, 220, 210, 0.95);
		box-shadow:
			4px 4px 12px rgba(166, 124, 124, 0.2),
			-4px -4px 12px rgba(255, 255, 255, 0.8),
			inset 1px 1px 3px rgba(255, 255, 255, 0.6);
	}

	:global(.neu-open-tab-btn:active) {
		transform: translateY(0) scale(1);
		background: rgba(220, 210, 200, 0.9);
		box-shadow:
			inset 2px 2px 6px rgba(166, 124, 124, 0.2),
			inset -2px -2px 6px rgba(255, 255, 255, 0.6);
	}

	/* 深色模式 Open Tab按钮 */
	:global(.dark) :global(.neu-open-tab-btn) {
		fill: #C4A8A6;
		background: rgba(70, 60, 55, 0.8);
		border: 1px solid rgba(255, 255, 255, 0.1);
		box-shadow:
			3px 3px 8px rgba(0, 0, 0, 0.4),
			-3px -3px 8px rgba(255, 255, 255, 0.04),
			inset 1px 1px 2px rgba(255, 255, 255, 0.08);
	}

	:global(.dark) :global(.neu-open-tab-btn:hover) {
		fill: #E0C4C2;
		background: rgba(80, 70, 65, 0.9);
		box-shadow:
			4px 4px 12px rgba(0, 0, 0, 0.5),
			-4px -4px 12px rgba(255, 255, 255, 0.06),
			inset 1px 1px 3px rgba(255, 255, 255, 0.1);
	}
</style>