/**
 * HTML5媒体检测和控制工具
 * 用于非YouTube网站的媒体元素检测和控制
 */

export interface HTML5MediaInfo {
    volume: number;
    isPaused: boolean;
    currentTime: number;
    duration: number;
    playbackRate: number;
    muted: boolean;
    hasAudio: boolean;
    hasVideo: boolean;
    videoWidth: number;
    videoHeight: number;
    videoResolution: string;
}

/**
 * 检测页面中的HTML5媒体元素
 */
export function detectHTML5Media(): HTMLMediaElement | null {
    // 优先查找video元素
    const videos = document.querySelectorAll('video');
    if (videos.length > 0) {
        // 返回第一个不是静音且有音频的视频元素
        for (const video of videos) {
            if (!video.muted && video.duration > 0) {
                return video;
            }
        }
        // 如果没有找到合适的，返回第一个视频元素
        return videos[0];
    }

    // 如果没有video元素，查找audio元素
    const audios = document.querySelectorAll('audio');
    if (audios.length > 0) {
        // 返回第一个有音频的audio元素
        for (const audio of audios) {
            if (audio.duration > 0) {
                return audio;
            }
        }
        return audios[0];
    }

    return null;
}

/**
 * 获取HTML5媒体元素的详细信息
 */
export function getHTML5MediaInfo(mediaElement: HTMLMediaElement): HTML5MediaInfo {
    const isVideo = mediaElement instanceof HTMLVideoElement;
    
    return {
        volume: Math.round(mediaElement.volume * 100),
        isPaused: mediaElement.paused,
        currentTime: mediaElement.currentTime,
        duration: mediaElement.duration || 0,
        playbackRate: mediaElement.playbackRate,
        muted: mediaElement.muted,
        hasAudio: true, // HTML5 audio/video元素默认有音频
        hasVideo: isVideo,
        videoWidth: isVideo ? (mediaElement as HTMLVideoElement).videoWidth : 0,
        videoHeight: isVideo ? (mediaElement as HTMLVideoElement).videoHeight : 0,
        videoResolution: isVideo && (mediaElement as HTMLVideoElement).videoWidth > 0 
            ? `${(mediaElement as HTMLVideoElement).videoWidth}×${(mediaElement as HTMLVideoElement).videoHeight}` 
            : '音频'
    };
}

/**
 * 控制HTML5媒体播放/暂停
 */
export async function toggleHTML5MediaPlayback(mediaElement: HTMLMediaElement): Promise<boolean> {
    try {
        if (mediaElement.paused) {
            await mediaElement.play();
            return false; // 现在是播放状态，所以isPaused = false
        } else {
            mediaElement.pause();
            return true; // 现在是暂停状态，所以isPaused = true
        }
    } catch (error) {
        console.error('控制播放失败:', error);
        return mediaElement.paused;
    }
}

/**
 * 设置HTML5媒体音量
 */
export function setHTML5MediaVolume(mediaElement: HTMLMediaElement, volume: number): void {
    mediaElement.volume = Math.max(0, Math.min(1, volume / 100));
}

/**
 * 设置HTML5媒体静音状态
 */
export function setHTML5MediaMuted(mediaElement: HTMLMediaElement, muted: boolean): void {
    mediaElement.muted = muted;
}

/**
 * 跳转到指定时间
 */
export function seekHTML5Media(mediaElement: HTMLMediaElement, time: number): void {
    if (time >= 0 && time <= mediaElement.duration) {
        mediaElement.currentTime = time;
    }
}

/**
 * 快进指定秒数
 */
export function forwardHTML5Media(mediaElement: HTMLMediaElement, seconds: number): void {
    const newTime = mediaElement.currentTime + seconds;
    seekHTML5Media(mediaElement, newTime);
}

/**
 * 快退指定秒数
 */
export function rewindHTML5Media(mediaElement: HTMLMediaElement, seconds: number): void {
    const newTime = mediaElement.currentTime - seconds;
    seekHTML5Media(mediaElement, newTime);
}

/**
 * 设置播放速度
 */
export function setHTML5MediaPlaybackRate(mediaElement: HTMLMediaElement, rate: number): void {
    mediaElement.playbackRate = Math.max(0.25, Math.min(4, rate));
}

/**
 * 检测页面是否有HTML5媒体元素
 */
export function hasHTML5Media(): boolean {
    return document.querySelectorAll('video, audio').length > 0;
}

/**
 * 为非YouTube站点执行的媒体检测脚本
 * 这个函数会被chrome.scripting.executeScript调用
 */
export function executeHTML5MediaDetection(): HTML5MediaInfo | null {
    const mediaElement = detectHTML5Media();
    if (!mediaElement) {
        return null;
    }
    return getHTML5MediaInfo(mediaElement);
}

/**
 * 为非YouTube站点执行媒体控制操作
 */
export function executeHTML5MediaControl(action: string, value?: number): any {
    const mediaElement = detectHTML5Media();
    if (!mediaElement) {
        return null;
    }

    switch (action) {
        case 'toggle':
            return toggleHTML5MediaPlayback(mediaElement);
        case 'setVolume':
            if (typeof value === 'number') {
                setHTML5MediaVolume(mediaElement, value);
                return mediaElement.volume * 100;
            }
            break;
        case 'setMuted':
            if (typeof value === 'boolean') {
                setHTML5MediaMuted(mediaElement, value);
                return mediaElement.muted;
            }
            break;
        case 'seek':
            if (typeof value === 'number') {
                seekHTML5Media(mediaElement, value);
                return mediaElement.currentTime;
            }
            break;
        case 'forward':
            if (typeof value === 'number') {
                forwardHTML5Media(mediaElement, value);
                return mediaElement.currentTime;
            }
            break;
        case 'rewind':
            if (typeof value === 'number') {
                rewindHTML5Media(mediaElement, value);
                return mediaElement.currentTime;
            }
            break;
        case 'setPlaybackRate':
            if (typeof value === 'number') {
                setHTML5MediaPlaybackRate(mediaElement, value);
                return mediaElement.playbackRate;
            }
            break;
        case 'getInfo':
            return getHTML5MediaInfo(mediaElement);
        default:
            return null;
    }
    return null;
}