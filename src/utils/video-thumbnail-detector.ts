/**
 * 视频封面检测工具
 * 用于非YouTube网站的视频封面获取
 */

export interface VideoThumbnailInfo {
    thumbnailUrl: string;
    title: string;
    siteName: string;
}

/**
 * 从页面元信息中提取视频封面
 */
export function extractVideoThumbnail(): VideoThumbnailInfo | null {
    try {
        // 1. 优先从Open Graph标签获取
        const ogImage = document.querySelector('meta[property="og:image"]')?.getAttribute('content');
        const ogTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content');
        const ogSiteName = document.querySelector('meta[property="og:site_name"]')?.getAttribute('content');

        // 2. 从Twitter Card标签获取
        const twitterImage = document.querySelector('meta[name="twitter:image"]')?.getAttribute('content');
        const twitterTitle = document.querySelector('meta[name="twitter:title"]')?.getAttribute('content');

        // 3. 从视频元素的poster属性获取
        const videoElement = document.querySelector('video[poster]') as HTMLVideoElement;
        const videoPoster = videoElement?.poster;

        // 4. 从页面标题获取
        const pageTitle = document.title;

        // 5. 从favicon获取站点名称
        const siteName = ogSiteName || twitterTitle || extractSiteNameFromUrl() || 'Unknown Site';

        // 优先级：video poster > og:image > twitter:image
        let thumbnailUrl = videoPoster || ogImage || twitterImage;

        // 确保URL是绝对路径
        if (thumbnailUrl && !thumbnailUrl.startsWith('http')) {
            const base = new URL(window.location.href);
            thumbnailUrl = new URL(thumbnailUrl, base).href;
        }

        if (thumbnailUrl) {
            return {
                thumbnailUrl,
                title: ogTitle || twitterTitle || pageTitle || '未知视频',
                siteName
            };
        }

        return null;
    } catch (error) {
        console.warn('提取视频封面失败:', error);
        return null;
    }
}

/**
 * 从URL中提取站点名称
 */
function extractSiteNameFromUrl(): string {
    try {
        const hostname = window.location.hostname;
        const parts = hostname.split('.');
        
        // 移除www前缀和常见TLD
        if (parts[0] === 'www') {
            parts.shift();
        }
        
        // 返回主域名部分
        return parts[0] || hostname;
    } catch {
        return 'Unknown';
    }
}

/**
 * 检测常用视频网站的特殊封面获取方法
 */
export function detectSpecialSiteThumbnail(): VideoThumbnailInfo | null {
    const hostname = window.location.hostname.toLowerCase();
    
    try {
        // Bilibili
        if (hostname.includes('bilibili.com')) {
            return getBilibiliThumbnail();
        }
        
        // 腾讯视频
        if (hostname.includes('qq.com') && window.location.pathname.includes('video')) {
            return getTencentVideoThumbnail();
        }
        
        // 爱奇艺
        if (hostname.includes('iqiyi.com')) {
            return getIqiyiThumbnail();
        }
        
        // 优酷
        if (hostname.includes('youku.com')) {
            return getYoukuThumbnail();
        }
        
        return null;
    } catch (error) {
        console.warn('检测特殊站点封面失败:', error);
        return null;
    }
}

/**
 * 获取Bilibili视频封面
 */
function getBilibiliThumbnail(): VideoThumbnailInfo | null {
    try {
        // Bilibili的封面通常在这些选择器中
        const thumbnailSelectors = [
            '.bpx-player-video-poster',
            '.video-poster img',
            '.bilibili-player-video-poster img',
            '.player-wrapper .poster img'
        ];
        
        for (const selector of thumbnailSelectors) {
            const img = document.querySelector(selector) as HTMLImageElement;
            if (img?.src) {
                const title = document.querySelector('h1[title], .video-title')?.textContent?.trim() || 
                             document.title.replace(' - 哔哩哔哩', '');
                
                return {
                    thumbnailUrl: img.src,
                    title,
                    siteName: '哔哩哔哩'
                };
            }
        }
        
        return null;
    } catch {
        return null;
    }
}

/**
 * 获取腾讯视频封面
 */
function getTencentVideoThumbnail(): VideoThumbnailInfo | null {
    try {
        const poster = document.querySelector('.txp_poster img, .video-poster img') as HTMLImageElement;
        if (poster?.src) {
            const title = document.querySelector('.player_title, .video-title')?.textContent?.trim() || 
                         document.title.replace(' - 腾讯视频', '');
            
            return {
                thumbnailUrl: poster.src,
                title,
                siteName: '腾讯视频'
            };
        }
        
        return null;
    } catch {
        return null;
    }
}

/**
 * 获取爱奇艺封面
 */
function getIqiyiThumbnail(): VideoThumbnailInfo | null {
    try {
        const poster = document.querySelector('.iqp-poster img, .video-poster img') as HTMLImageElement;
        if (poster?.src) {
            const title = document.querySelector('.info-title, .video-title')?.textContent?.trim() || 
                         document.title.replace('_爱奇艺', '');
            
            return {
                thumbnailUrl: poster.src,
                title,
                siteName: '爱奇艺'
            };
        }
        
        return null;
    } catch {
        return null;
    }
}

/**
 * 获取优酷封面
 */
function getYoukuThumbnail(): VideoThumbnailInfo | null {
    try {
        const poster = document.querySelector('.youku-player-poster img, .video-poster img') as HTMLImageElement;
        if (poster?.src) {
            const title = document.querySelector('.title-wrap, .video-title')?.textContent?.trim() || 
                         document.title.replace('_优酷', '');
            
            return {
                thumbnailUrl: poster.src,
                title,
                siteName: '优酷'
            };
        }
        
        return null;
    } catch {
        return null;
    }
}

/**
 * 获取视频封面的主函数
 */
export function getVideoThumbnail(): VideoThumbnailInfo | null {
    // 1. 先尝试特殊网站的检测
    const specialThumbnail = detectSpecialSiteThumbnail();
    if (specialThumbnail) {
        return specialThumbnail;
    }
    
    // 2. 使用通用方法提取
    return extractVideoThumbnail();
}

/**
 * 为扩展脚本注入使用的函数
 */
export function executeVideoThumbnailDetection(): VideoThumbnailInfo | null {
    return getVideoThumbnail();
}